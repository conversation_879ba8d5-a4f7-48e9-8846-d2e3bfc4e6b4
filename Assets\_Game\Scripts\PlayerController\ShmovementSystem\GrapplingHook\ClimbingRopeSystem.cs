using UnityEngine;
using System.Collections;
using KinematicCharacterController.FPS;
using AudioSystem;

public class RopeClimbingSystem : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private Transform gunTip;
    [SerializeField] private Transform camera;
    [SerializeField] private Transform player;
    [SerializeField] private LayerMask grapplableMask;
    [SerializeField] private LineRenderer lineRenderer;
    [SerializeField] private Transform predictionPoint;
    [SerializeField] private FPSCharacterController playerMovement;
    [SerializeField] private PredictionVisualizer predictionVisualizer;
    
    // Audio reference
    private PlayerAudioHandler playerAudioHandler;

    [Header("Rope Settings")]
    [SerializeField] private float maxRopeDistance = 25f;
    [SerializeField] private float ropeThrowSpeed = 2f; // Time to deploy
    [SerializeField] private float ropeRetractSpeed = 1f; // Time to retract

    [Header("Climbing")]
    [SerializeField] private float climbSpeed = 4f;
    [SerializeField] private float reelInSpeed = 6f;
    [SerializeField] private float reelOutSpeed = 4f;
    [SerializeField] private float ropeConstraintForce = 100f;

    [Header("Prediction")]
    [SerializeField] private float predictionSphereCastRadius = 2f;
    [SerializeField] private Material predictionMaterial;

    [Header("Sphere Visualization")]
    [SerializeField] private float baseSphereSize = 0.3f;
    [SerializeField] private Color sphereColor = Color.yellow;
    [SerializeField] private float distanceScaleFactor = 0.05f;
    [SerializeField] private float positionSmoothTime = 0.1f;

    private Vector3 ropeAttachPoint;
    private float currentRopeLength;
    private float targetRopeLength;
    
    private RaycastHit predictionHit;
    private bool predictionFound;

    private bool isRopeAttached = false;
    public bool IsRopeAttached { get { return isRopeAttached; } }

    private bool isPredictionEnabled = false;
    private bool isClimbing = false;

    private MeshRenderer sphereRenderer;
    private Material sphereMaterial;

    private Vector3 targetSpherePosition;
    private Vector3 currentSphereVelocity = Vector3.zero;

    // Rope deployment
    private bool isDeploying = false;
    private bool isRetracting = false;
    private float deploymentProgress = 0f;

    private void Awake()
    {
        if (playerMovement == null)
            playerMovement = GetComponent<FPSCharacterController>();

        if (camera == null && Camera.main != null)
            camera = Camera.main.transform;

        if (player == null)
            player = transform;

        if (lineRenderer == null)
        {
            lineRenderer = gameObject.AddComponent<LineRenderer>();
            SetupLineRenderer();
        }

        if (predictionVisualizer == null)
            predictionVisualizer = GetComponent<PredictionVisualizer>();

        // Get PlayerAudioHandler reference
        if (playerAudioHandler == null)
            playerAudioHandler = GetComponent<PlayerAudioHandler>();

        if (predictionPoint == null)
        {
            CreatePredictionSphere();
        }
    }

    private void CreatePredictionSphere()
    {
        GameObject sphereObj = new GameObject("RopePredictionSphere");
        predictionPoint = sphereObj.transform;

        GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        sphere.transform.SetParent(predictionPoint);
        sphere.transform.localPosition = Vector3.zero;
        sphere.transform.localScale = Vector3.one * baseSphereSize;

        sphereRenderer = sphere.GetComponent<MeshRenderer>();

        if (predictionMaterial != null)
        {
            sphereMaterial = new Material(predictionMaterial);
        }
        else
        {
            sphereMaterial = new Material(Shader.Find("Standard"));
            sphereMaterial.EnableKeyword("_EMISSION");
            sphereMaterial.SetColor("_EmissionColor", sphereColor * 2.0f);
        }

        sphereMaterial.color = sphereColor;
        sphereRenderer.material = sphereMaterial;

        Destroy(sphere.GetComponent<Collider>());

        sphereObj.SetActive(false);

        targetSpherePosition = predictionPoint.position;
    }

    private void UpdateSphereVisualization(RaycastHit hit)
    {
        if (predictionPoint == null) return;

        targetSpherePosition = hit.point + hit.normal * 0.05f;

        predictionPoint.position = Vector3.SmoothDamp(
            predictionPoint.position,
            targetSpherePosition,
            ref currentSphereVelocity,
            positionSmoothTime
        );

        float distance = Vector3.Distance(camera.position, hit.point);
        float adaptiveSize = baseSphereSize + (distance * distanceScaleFactor);
        adaptiveSize = Mathf.Clamp(adaptiveSize, baseSphereSize * 0.5f, baseSphereSize * 3f);

        predictionPoint.localScale = Vector3.one * adaptiveSize;
    }

    private void SetupLineRenderer()
    {
        lineRenderer.startWidth = 0.05f;
        lineRenderer.endWidth = 0.05f;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = new Color(0.4f, 0.3f, 0.2f); // Brown rope color
        lineRenderer.endColor = new Color(0.3f, 0.2f, 0.1f);
        lineRenderer.positionCount = 0;
    }

    private void Update()
    {
        if (isPredictionEnabled)
        {
            if (predictionVisualizer != null)
            {
                predictionFound = predictionVisualizer.HasPredictionPoint();
                if (predictionFound)
                    predictionHit = predictionVisualizer.GetPredictionHit();
            }
            else
            {
                CheckForAttachPoint();
            }
        }
        else
        {
            if (predictionPoint != null)
            {
                predictionPoint.gameObject.SetActive(false);
            }
            predictionFound = false;
        }

        if (isRopeAttached)
        {
            HandleRopeConstraint();
            HandleClimbing();
        }

        // Update deployment animation
        if (isDeploying || isRetracting)
        {
            UpdateRopeDeployment();
        }
    }

    private void LateUpdate()
    {
        DrawRope();
    }

    public void FireGrapple()
    {
        if (isRopeAttached || isDeploying || isRetracting) return;

        bool canAttach = false;
        Vector3 attachTarget = Vector3.zero;

        if (predictionVisualizer != null && predictionVisualizer.HasPredictionPoint())
        {
            canAttach = true;
            attachTarget = predictionVisualizer.GetPredictionPoint();
        }
        else if (predictionFound)
        {
            canAttach = true;
            attachTarget = predictionHit.point;
        }

        if (canAttach)
        {
            // Play the grapple fire sound
            if (playerAudioHandler != null)
            {
                playerAudioHandler.OnGrappleFire();
            }
            
            ropeAttachPoint = attachTarget;
            currentRopeLength = Vector3.Distance(player.position, ropeAttachPoint);
            targetRopeLength = currentRopeLength;
            
            // Start deployment animation
            isDeploying = true;
            deploymentProgress = 0f;
            lineRenderer.positionCount = 2;
        }
    }

    public void ReleaseGrapple()
    {
        if (isRopeAttached && !isRetracting)
        {
            isRopeAttached = false;
            isClimbing = false;
            isRetracting = true;
            deploymentProgress = 1f;
        }
    }

    private void CheckForAttachPoint()
    {
        if (isRopeAttached || !isPredictionEnabled) return;

        predictionFound = false;

        RaycastHit raycastHit;
        bool rayHit = Physics.Raycast(camera.position, camera.forward, out raycastHit, maxRopeDistance, grapplableMask);

        RaycastHit spherecastHit;
        bool sphereHit = Physics.SphereCast(camera.position, predictionSphereCastRadius, camera.forward,
            out spherecastHit, maxRopeDistance, grapplableMask);

        if (rayHit)
        {
            predictionHit = raycastHit;
            predictionFound = true;
        }
        else if (sphereHit)
        {
            predictionHit = spherecastHit;
            predictionFound = true;
        }

        if (predictionPoint != null)
        {
            if (predictionFound)
            {
                if (!predictionPoint.gameObject.activeSelf)
                {
                    predictionPoint.gameObject.SetActive(true);
                    predictionPoint.position = predictionHit.point + predictionHit.normal * 0.05f;
                    targetSpherePosition = predictionPoint.position;
                    currentSphereVelocity = Vector3.zero;
                }
                UpdateSphereVisualization(predictionHit);
            }
            else
            {
                predictionPoint.gameObject.SetActive(false);
            }
        }
    }

    private void UpdateRopeDeployment()
    {
        if (isDeploying)
        {
            deploymentProgress += Time.deltaTime / ropeThrowSpeed;
            if (deploymentProgress >= 1f)
            {
                deploymentProgress = 1f;
                isDeploying = false;
                isRopeAttached = true;
                
                if (predictionPoint != null)
                    predictionPoint.gameObject.SetActive(false);
            }
        }
        else if (isRetracting)
        {
            deploymentProgress -= Time.deltaTime / ropeRetractSpeed;
            if (deploymentProgress <= 0f)
            {
                deploymentProgress = 0f;
                isRetracting = false;
                lineRenderer.positionCount = 0;
            }
        }
    }

    private void HandleRopeConstraint()
    {
        if (!isRopeAttached) return;

        float distanceToAttachPoint = Vector3.Distance(player.position, ropeAttachPoint);
        
        // Apply constraint if player exceeds rope length
        if (distanceToAttachPoint > targetRopeLength)
        {
            Vector3 directionToAttachPoint = (ropeAttachPoint - player.position).normalized;
            float overExtension = distanceToAttachPoint - targetRopeLength;
            
            // Apply force to pull player back
            Vector3 constraintForce = directionToAttachPoint * overExtension * ropeConstraintForce;
            playerMovement.AddVelocity(constraintForce * Time.deltaTime);
        }
    }

    private void HandleClimbing()
    {
        if (!isRopeAttached) return;

        // Climbing up/down
        if (Input.GetKey(KeyCode.W))
        {
            if (isClimbing)
            {
                // Climb up the rope
                Vector3 climbDirection = (ropeAttachPoint - player.position).normalized;
                playerMovement.AddVelocity(climbDirection * climbSpeed);
            }
        }
        else if (Input.GetKey(KeyCode.S))
        {
            if (isClimbing)
            {
                // Climb down the rope
                Vector3 climbDirection = (player.position - ropeAttachPoint).normalized;
                playerMovement.AddVelocity(climbDirection * climbSpeed);
            }
        }

        // Toggle climbing mode with Shift
        if (Input.GetKeyDown(KeyCode.LeftShift))
        {
            isClimbing = !isClimbing;
        }

        // Reel in/out with Space/Ctrl
        if (Input.GetKey(KeyCode.Space))
        {
            targetRopeLength -= reelInSpeed * Time.deltaTime;
            targetRopeLength = Mathf.Max(2f, targetRopeLength);
        }
        else if (Input.GetKey(KeyCode.LeftControl))
        {
            targetRopeLength += reelOutSpeed * Time.deltaTime;
            targetRopeLength = Mathf.Min(maxRopeDistance, targetRopeLength);
        }

        // Smoothly adjust actual rope length
        currentRopeLength = Mathf.Lerp(currentRopeLength, targetRopeLength, Time.deltaTime * 5f);
    }

    private void DrawRope()
    {
        if (lineRenderer.positionCount != 2) return;

        if (isDeploying || isRetracting || isRopeAttached)
        {
            Vector3 endPoint = ropeAttachPoint;
            
            // During deployment/retraction, animate the rope
            if (isDeploying || isRetracting)
            {
                endPoint = Vector3.Lerp(gunTip.position, ropeAttachPoint, deploymentProgress);
            }

            lineRenderer.SetPosition(0, gunTip.position);
            lineRenderer.SetPosition(1, endPoint);
        }
    }

    private void OnDisable()
    {
        if (isRopeAttached)
        {
            ReleaseGrapple();
        }
    }

    public void SetPredictionEnabled(bool enabled)
    {
        isPredictionEnabled = enabled;

        if (!enabled && predictionPoint != null)
        {
            predictionPoint.gameObject.SetActive(false);
        }
    }
}